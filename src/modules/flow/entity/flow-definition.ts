import { Entity } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';
import { Column, Index, Unique } from 'typeorm';

/**
 * 流程节点类型
 */
export type NodeType =
  | 'start'
  | 'between'
  | 'serial'
  | 'parallel'
  | 'serial-node'
  | 'end';

/**
 * 节点属性
 */
export interface NodeProperties {
  nodeRatioType?: string;
  backType?: string;
  nodeRatio?: number;
  permissions?: string[];
  combination?: {
    role: Array<{
      id: number;
      name: string;
    }>;
    user: Array<{
      id: number;
      name: string;
    }>;
    form: Array<{
      id: number;
      name?: {
        externalFields: string[];
        formFields: string[];
        candidateUserField: string;
        externalFormName: string;
        externalFormId: number;
      };
    }>;
  };
  emptyApprove?: {
    type: 'USER' | 'AUTO' | 'REJECT' | 'MANAGER';
    value: Array<{
      id: number;
      name: string;
    }>;
  };
  value?: string;
  buttons?: Array<{
    type: string;
    checked: boolean;
    text: string;
  }>;
  backTypeNode?: string;
  formColumns?: any[];
  fieldSetting?: any[];
  conditions?: {
    simple: boolean;
    group: string;
    simpleData: Array<{
      key: string;
      cond: string;
      value: string;
      next: string;
    }>;
  };
  managers?: Array<{
    id: number;
    name: string;
  }>;
}

/**
 * 流程节点
 */
export interface FlowNode {
  nodeId: string;
  nodeType: NodeType;
  nodeName: string;
  value?: string;
  type?: string;
  properties: NodeProperties;
  childNode?: FlowNode;
  conditionNodes?: FlowNode[];
  sortNum?: number;
  default?: boolean;
  levelValue?: string;
  enableDestory?: boolean;
}

export interface backfillConfig {
  backfillFields: string[];
  sources: {
    role: Array<{
      id: number;
      name: string;
    }>;
    user: Array<{
      id: number;
      name: string;
    }>;
    form: Array<{
      id: number;
      name: {
        externalFields: string[];
        formFields: string[];
        candidateUserField: string;
        externalFormName: string;
        externalFormId: number;
      };
    }>;
  };
  conditions?: string;
}

@Entity('flow_definition')
@Unique(['formId', 'version'])
export class FlowDefinitionEntity extends BaseEntity {
  @Index()
  @Column({ comment: '流程名称' })
  name: string;

  @Column({ comment: '流程版本', default: 1 })
  version: number;

  @Column({ comment: '表单id' })
  formId: number;

  @Column({ comment: '流程管理员', type: 'json', nullable: true })
  managers: Array<{
    id: number;
    name: string;
  }>;
  @Column({ type: 'json', comment: '流程定义' })
  flowJson: {
    nodeId: string;
    nodeType: NodeType;
    nodeName: string;
    value: string;
    properties: NodeProperties;
    childNode: FlowNode;
  };

  @Column({ type: 'json', comment: '补录提醒配置', nullable: true })
  backfillConfig: backfillConfig;

  @Column({ comment: '补录通知间隔', default: -1, nullable: true })
  backfillNoticeInterval: number;

  @Column({ comment: '流程久悬通知时间/天', default: -1 })
  flowUnDoneNoticeDay: number;

  @Index()
  @Column({ comment: '是否激活', default: true })
  active: boolean;

  @Column({ comment: '流程描述', nullable: true })
  description: string;
}
