import { Init, Provide, Inject } from '@midwayjs/core';
import { ILogger } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository, In, Raw } from 'typeorm';
import {
  FlowDefinitionEntity,
  backfillConfig,
} from '../entity/flow-definition';
import { CoolCommException } from '@cool-midway/core';
import { FlowInstanceEntity } from '../entity/flow-instance';
import { NoticeService } from '../../messages/service/notice';
import { NotificationEntity } from '../../messages/entity/notification';
import { MESSAGE_TYPE, RECEIVER_TYPE } from '../../messages/constant';
import { CloudDBEntity } from '../../cloud/entity/db';
import { BaseSysUserRoleEntity } from '../../base/entity/sys/user_role';
import { CloudDBService } from '../../cloud/service/db';
import { AUDIT_STATUS } from '../../cloud/constants';
import { BaseSysUserEntity } from '../../base/entity/sys/user';
import { UserNotificationEntity } from '../../messages/entity/user_notification';
import { SqlValidatorService } from '../../quality/service/sql_validator';
/**
 * 补录提醒服务
 */
@Provide()
export class BackfillService extends BaseService {
  @InjectEntityModel(FlowDefinitionEntity)
  flowDefinitionEntity: Repository<FlowDefinitionEntity>;

  @InjectEntityModel(FlowInstanceEntity)
  flowInstanceEntity: Repository<FlowInstanceEntity>;

  @InjectEntityModel(CloudDBEntity)
  cloudDBEntity: Repository<CloudDBEntity>;
  @Inject()
  logger: ILogger;
  @InjectEntityModel(BaseSysUserRoleEntity)
  userRoleEntity: Repository<BaseSysUserRoleEntity>;
  @Inject()
  noticeService: NoticeService;
  @InjectEntityModel(NotificationEntity)
  notificationEntity: Repository<NotificationEntity>;
  @Inject()
  cloudDBService: CloudDBService;
  @InjectEntityModel(UserNotificationEntity)
  userNotificationEntity: Repository<UserNotificationEntity>;
  @Inject()
  sqlValidatorService: SqlValidatorService;
  /**
   * 发送久悬通知
   */
  async sendFlowUnDoneNotice() {
    const query = this.flowInstanceEntity
      .createQueryBuilder('flow_instance')
      .leftJoinAndSelect(
        FlowDefinitionEntity,
        'flow_definition',
        'flow_instance.definitionId = flow_definition.id'
      )
      .leftJoinAndSelect(
        CloudDBEntity,
        'cloud_db',
        'flow_definition.formId = cloud_db.id'
      )
      .select([
        'flow_instance.id as flowInstanceId',
        'flow_instance.initiatorId as initiatorId',
        'flow_instance.initiatorName as initiatorName',
        'flow_instance.businessId as businessId',
        'flow_instance.formId as formId',
        'flow_instance.currentNodeId as currentNodeId',
        'flow_instance.updateTime as updateTime',
        'flow_definition.name as flowName',
        'flow_definition.flowUnDoneNoticeDay as flowUnDoneNoticeDay',
        'cloud_db.name as formName',
      ])
      .where('flow_instance.status = :status', { status: 'running' })
      .andWhere('flow_definition.flowUnDoneNoticeDay > 0')
      .andWhere(
        'flow_instance.updateTime < DATE_SUB(NOW(), INTERVAL flow_definition.flowUnDoneNoticeDay DAY)'
      )
      .andWhere(qb => {
        const subQuery = qb
          .subQuery()
          .select('1')
          .from(NotificationEntity, 'notification')
          .where('notification.noticeType = :noticeType', {
            noticeType: MESSAGE_TYPE.OVERDUE,
          })
          // 处理 JSON 字段查询（MySQL JSON_EXTRACT 语法）
          .andWhere(
            'JSON_EXTRACT(notification.businessData, "$.instanceId") = flow_instance.id'
          )
          .getQuery();

        return `NOT EXISTS ${subQuery}`;
      });
    // 执行查询
    const unDoneFlows = await query.getRawMany();
    // 发送通知
    for (const flow of unDoneFlows) {
      await this.noticeService.sendNotification({
        receivers: [
          {
            type: RECEIVER_TYPE.USER,
            ids: [flow.initiatorId],
          },
        ],
        noticeType: MESSAGE_TYPE.OVERDUE,
        title: '流程久悬提醒',
        content: `您发起的流程"${flow.formName}"已超过${flow.flowUnDoneNoticeDay}天未完成，请及时联系办理人。`,
        businessData: {
          instanceId: flow.flowInstanceId,
          businessId: flow.businessId,
        },
      });
    }

    return `发送久悬通知${unDoneFlows.length}条`;
  }

  async validateFormConfig(
    formConfig: {
      externalFields: string[];
      formFields: string[];
      candidateUserField: string;
      externalFormName: string;
      externalFormId: number;
    },
    formRepo: Repository<any>,
    externRepo: Repository<any>
  ) {
    const {
      externalFields,
      formFields,
      candidateUserField,
      externalFormName,
      externalFormId,
    } = formConfig;
    if (externalFields.length !== formFields.length) {
      throw new CoolCommException(
        `外部字段和表单字段数量不一致,外部字段数量为${externalFields.length},表单字段数量为${formFields.length}`
      );
    }
    if (candidateUserField.length !== 1) {
      throw new CoolCommException(
        `候选人字段数量只能为1,当前为${candidateUserField.length}`
      );
    }
    const formExistsFields = formRepo.metadata.ownColumns.map(
      (item: any) => item.propertyName
    );
    const externExistsFields = externRepo.metadata.ownColumns.map(
      (item: any) => item.propertyName
    );
    if (!externalFields.every(field => externExistsFields.includes(field))) {
      throw new CoolCommException(
        `外部字段${externalFields
          .filter(field => !externExistsFields.includes(field))
          .join(',')}不存在`
      );
    }
    if (!formFields.every(field => formExistsFields.includes(field))) {
      this.logger.debug(
        `表单字段${formFields},formExistsFields:${formExistsFields}`
      );
      throw new CoolCommException(
        `表单字段${formFields
          .filter(field => !formExistsFields.includes(field))
          .join(',')}不存在`
      );
    }
    if (!externExistsFields.includes(candidateUserField[0])) {
      throw new CoolCommException(
        `候选人字段${candidateUserField[0]}不存在,外部表单:${externalFormName},字段:${externExistsFields})}`
      );
    }
  }

  async backfillQueryFormApprovers(
    formId: number,
    formConfig: {
      externalFields: string[];
      formFields: string[];
      candidateUserField: string;
      externalFormName: string;
      externalFormId: number;
    },
    fields: string[],
    needBackfillSql?: string
  ) {
    const { externalFields, formFields, candidateUserField, externalFormId } =
      formConfig;
    let externRepo: Repository<any>;
    let formRepo: Repository<any>;
    try {
      externRepo = (
        await this.cloudDBService.getRepositoryByFormId(externalFormId)
      ).repository;
      formRepo = (await this.cloudDBService.getRepositoryByFormId(formId))
        .repository;
    } catch (error) {
      this.logger.error(`获取补录配置失败,formId:${formId},error:${error}`);
    }
    try {
      await this.validateFormConfig(formConfig, formRepo, externRepo);
    } catch (error) {
      // this.logger.error(`验证补录配置失败,formId:${formId},error:${error}`);
      throw new CoolCommException(`验证补录配置失败,error:${error}`);
    }
    const joinConditions = externalFields
      .map((field, index) => `e.${field} = f.${formFields[index]}`)
      .join(' AND ');
    const whereConditions = fields
      .map(field => `f.${field} is null`)
      .join(' OR ');

    const find = formRepo
      .createQueryBuilder('f')
      .leftJoin(externRepo.metadata.tableName, 'e', joinConditions)
      .leftJoin(BaseSysUserEntity, 'u', `e.${candidateUserField} = u.username`)
      .where(`(${whereConditions})`)
      .andWhere('f.审核状态 = :status', { status: AUDIT_STATUS.APPROVED })
      .andWhere('u.id IS NOT NULL')
      .andWhere('u.id != :emptyString', { emptyString: '' });

    // 如果指定了needBackfillSql，则使用EXISTS子查询进行筛选
    if (needBackfillSql && needBackfillSql.trim()) {
      // 验证SQL
      await this.sqlValidatorService.validateSql(needBackfillSql);

      // 检查SQL中是否包含id字段
      const sqlLower = needBackfillSql.toLowerCase();
      if (!sqlLower.includes('select') || !sqlLower.includes('id')) {
        throw new CoolCommException('needBackfillSql必须是SELECT语句且必须包含id字段');
      }

      // 使用EXISTS子查询优化性能
      find.andWhere(`EXISTS (SELECT 1 FROM (${needBackfillSql}) AS backfill_ids WHERE backfill_ids.id = f.id)`);
    }

    const result = await find
      .select(['u.id as userId'])
      .distinct(true)
      .orderBy('f.createTime', 'DESC')
      .getRawMany();

    return result.map((item: any) => item.userId);
  }

  /**
   * 执行SQL获取需要补录的记录ID列表
   * @param needBackfillSql SQL语句
   * @returns ID列表
   */
  async executeBackfillSql(needBackfillSql: string): Promise<string[]> {
    try {
      // 验证SQL
      await this.sqlValidatorService.validateSql(needBackfillSql);

      // 检查SQL中是否包含id字段
      const sqlLower = needBackfillSql.toLowerCase();
      if (!sqlLower.includes('select') || !sqlLower.includes('id')) {
        throw new CoolCommException('needBackfillSql必须是SELECT语句且必须包含id字段');
      }

      // 执行SQL获取结果
      const result = await this.nativeQuery(`SELECT distinct id FROM (${needBackfillSql}) AS temp`);

      return result.map((item: any) => String(item.id)).filter((id: string) => id);
    } catch (error) {
      this.logger.error(`执行补录SQL失败,sql:${needBackfillSql},error:${error}`);
      throw new CoolCommException(`执行补录SQL失败: ${error.message}`);
    }
  }

  /**
   * 检查是否有需要补录的记录
   * @param formId 表单ID
   * @param backfillFields 补录字段
   * @param needBackfillSql 需要补录的记录SQL语句
   * @returns 是否有需要补录的记录
   */
  async checkHasBackfillRecords(
    formId: number,
    backfillFields: string[],
    needBackfillSql?: string
  ): Promise<boolean> {
    try {
      const formRepo = (await this.cloudDBService.getRepositoryByFormId(formId))
        .repository;

      const whereConditions = backfillFields
        .map(field => `${field} is null`)
        .join(' OR ');

      const query = formRepo
        .createQueryBuilder('f')
        .where(`(${whereConditions})`)
        .andWhere('审核状态 = :status', { status: AUDIT_STATUS.APPROVED });

      // 如果指定了needBackfillSql，则执行SQL获取ID列表并进一步筛选
      if (needBackfillSql && needBackfillSql.trim()) {
        const ids = await this.executeBackfillSql(needBackfillSql);
        if (ids.length === 0) {
          return false;
        }
        query.andWhere('id IN (:...ids)', { ids });
      }

      const count = await query.getCount();
      return count > 0;
    } catch (error) {
      this.logger.error(`检查补录记录失败,formId:${formId},error:${error}`);
      return false;
    }
  }

  /**
   * 获取所有补录通知用户
   * @param sources 补录配置
   * @param formId 表单ID
   * @param backfillFields 补录字段
   * @param needBackfillSql 需要补录的记录SQL语句
   * @returns 用户ID列表
   */
  async getAllBackfillNotifyUsers(
    sources: any,
    formId: number,
    backfillFields: string[],
    needBackfillSql?: string
  ) {
    const notifityUsers = [];

    // 检查是否有需要补录的记录
    const hasBackfillRecords = await this.checkHasBackfillRecords(
      formId,
      backfillFields,
      needBackfillSql
    );

    // 如果没有需要补录的记录，返回空数组
    if (!hasBackfillRecords) {
      return [];
    }

    if (sources.role.length > 0) {
      const userRoles = await this.userRoleEntity.find({
        where: {
          roleId: In(sources.role.map((item: any) => item.id)),
        },
      });
      notifityUsers.push(...userRoles.map((item: any) => item.userId));
    }
    if (sources.user.length > 0) {
      notifityUsers.push(...sources.user.map((item: any) => item.id));
    }
    if (sources.form.length > 0) {
      const formConfig = sources.form[0].name;
      const userIds = await this.backfillQueryFormApprovers(
        formId,
        formConfig,
        backfillFields,
        needBackfillSql
      );
      //  console.log('userIds', userIds, formConfig);
      notifityUsers.push(...userIds);
    }
    return notifityUsers;
  }
  /**
   * 发送补录提醒
   */
  async sendBackfillNotice() {
    const find = this.flowDefinitionEntity
      .createQueryBuilder('flow_definition')
      .leftJoinAndSelect(
        CloudDBEntity,
        'cloud_db',
        'flow_definition.formId = cloud_db.id'
      )
      .where('flow_definition.backfillConfig is not null')
      .andWhere('flow_definition.active = 1')
      .select([
        'flow_definition.id as definitionId',
        'flow_definition.backfillConfig as backfillConfig',
        'flow_definition.backfillNoticeInterval as backfillNoticeInterval',
        'cloud_db.name as formName',
        'cloud_db.id as formId',
        'cloud_db.colInfo as colInfo',
      ]);
    // this.logger.debug(find.getQueryAndParameters());
    let msg = '';
    const backfillTables: any[] = await find.getRawMany();
    for (const backfillTable of backfillTables) {
      const backfillConfig: backfillConfig = backfillTable.backfillConfig;
      const formId = backfillTable.formId;
      const backfillNoticeIntervalDays = backfillTable.backfillNoticeInterval;
      const formName = backfillTable.formName;
      const backfillFields = backfillConfig.backfillFields;
      const sources = backfillConfig.sources;
      const needBackfillSql = backfillConfig.needBackfillSqlIds;
      const notifityUserIds = await this.getAllBackfillNotifyUsers(
        sources,
        formId,
        backfillFields,
        needBackfillSql
      );

      const backfillNoticeExist = await this.notificationEntity
        .createQueryBuilder('notification')
        .select('notification.id')
        .where('notification.noticeType = :noticeType', {
          noticeType: MESSAGE_TYPE.BACKFILL,
        })
        .andWhere(
          'JSON_EXTRACT(notification.businessData, "$.formId") = :formId',
          { formId }
        )
        .getMany();
      if (notifityUserIds.length === 0) {
        if (backfillNoticeExist.length > 0) {
          const notificationIds = backfillNoticeExist.map(n => n.id);

          // 删除关联的用户通知
          await this.userNotificationEntity
            .createQueryBuilder()
            .delete()
            .where('notificationId IN (:...ids)', { ids: notificationIds })
            .execute();

          // 删除通知实体
          await this.notificationEntity
            .createQueryBuilder()
            .delete()
            .where('id IN (:...ids)', { ids: notificationIds })
            .execute();
        }

        msg += `表单:${formName},候选处理人为空,已删除相关通知记录\n`;
        continue;
      }
      //  如果从未发送过通知，则直接发送补录通知
      if (backfillNoticeExist.length === 0) {
        await this.sendBackfillNotification(
          notifityUserIds,
          formName,
          backfillFields,
          backfillTable.formId
        );
      } else {
        // 获取用户通知记录，检查updateTime
        const userNotifications = await this.userNotificationEntity.find({
          where: {
            notificationId: In(backfillNoticeExist.map(item => item.id)),
          },
          order: {
            updateTime: 'DESC',
          },
          take: 1,
        });

        const now = new Date();
        // 如果没有用户通知记录或最后更新时间已超过间隔天数，则更新updateTime
        if (
          now.getTime() - new Date(userNotifications[0].updateTime).getTime() >=
          backfillNoticeIntervalDays * 24 * 60 * 60 * 1000
        ) {
          await this.userNotificationEntity.update(
            {
              notificationId: In(backfillNoticeExist.map(item => item.id)),
            },
            { updateTime: now }
          );
        }
      }
    }
    return msg === '' ? '发送补录通知成功' : msg;
  }

  private async sendBackfillNotification(
    userIds: number[],
    formName: string,
    backfillFields: string[],
    formId: number
  ) {
    // this.logger.debug(
    //   `发送补录通知,userIds:${userIds},formName:${formName},backfillFields:${backfillFields},formId:${formId},maxUpdateTime:${maxUpdateTime}`
    // );
    await this.noticeService.sendNotification({
      receivers: [
        {
          type: RECEIVER_TYPE.USER,
          ids: userIds,
        },
      ],
      noticeType: MESSAGE_TYPE.BACKFILL,
      title: '补录提醒',
      content: `表单"${formName}"的"${backfillFields.join(',')}"字段需要补录`,
      businessData: {
        formId,
        backfillFields,
      },
    });
  }
}
